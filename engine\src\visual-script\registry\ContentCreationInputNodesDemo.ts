/**
 * 内容创作与输入节点演示程序
 * 展示批次10节点的功能和使用方法
 */

import { NodeRegistry } from './NodeRegistry';
import { ContentCreationInputNodesRegistry } from './ContentCreationInputNodesRegistry';
import { Vector3, Color } from 'three';

/**
 * 内容创作与输入节点演示类
 */
export class ContentCreationInputNodesDemo {
  private nodeRegistry: NodeRegistry;
  private registry: ContentCreationInputNodesRegistry;

  constructor() {
    this.nodeRegistry = new NodeRegistry();
    this.registry = new ContentCreationInputNodesRegistry(this.nodeRegistry);
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('🎨 内容创作与输入节点演示开始');
    console.log('='.repeat(50));

    try {
      // 注册所有节点
      await this.registerNodes();

      // 演示地形编辑功能
      await this.demonstrateTerrainEditing();

      // 演示动画编辑功能
      await this.demonstrateAnimationEditing();

      // 演示水体系统功能
      await this.demonstrateWaterSystem();

      // 演示VR/AR输入功能
      await this.demonstrateVRARInput();

      // 演示手势识别功能
      await this.demonstrateGestureRecognition();

      // 演示组件系统功能
      await this.demonstrateComponentSystem();

      // 演示变换系统功能
      await this.demonstrateTransformSystem();

      // 显示统计信息
      this.showStatistics();

      console.log('✅ 内容创作与输入节点演示完成');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 注册节点
   */
  private async registerNodes(): Promise<void> {
    console.log('📝 注册内容创作与输入节点...');
    
    this.registry.registerAllNodes();
    
    console.log(`✅ 成功注册 ${this.registry.getRegisteredNodeCount()} 个节点`);
    console.log('');
  }

  /**
   * 演示地形编辑功能
   */
  private async demonstrateTerrainEditing(): Promise<void> {
    console.log('🏔️ 地形编辑功能演示');
    console.log('-'.repeat(30));

    try {
      // 地形雕刻演示
      const sculptingNode = this.nodeRegistry.createNode('TerrainSculpting');
      if (sculptingNode) {
        const sculptResult = sculptingNode.execute({
          position: new Vector3(0, 0, 0),
          brushSize: 10,
          strength: 0.5,
          operation: 'raise'
        });
        console.log('🔨 地形雕刻结果:', sculptResult?.success ? '成功' : '失败');
      }

      // 地形纹理演示
      const textureNode = this.nodeRegistry.createNode('TerrainTexture');
      if (textureNode) {
        const textureResult = textureNode.execute({
          textureLayer: 0,
          blendMode: 'multiply',
          opacity: 0.8
        });
        console.log('🎨 地形纹理结果:', textureResult?.success ? '成功' : '失败');
      }

      // 地形植被演示
      const vegetationNode = this.nodeRegistry.createNode('TerrainVegetation');
      if (vegetationNode) {
        const vegResult = vegetationNode.execute({
          vegetationType: 'grass',
          density: 0.7,
          area: new Vector3(50, 0, 50)
        });
        console.log('🌱 地形植被结果:', vegResult?.success ? '成功' : '失败');
      }

      console.log('✅ 地形编辑功能演示完成\n');

    } catch (error) {
      console.error('❌ 地形编辑演示失败:', error);
    }
  }

  /**
   * 演示动画编辑功能
   */
  private async demonstrateAnimationEditing(): Promise<void> {
    console.log('🎬 动画编辑功能演示');
    console.log('-'.repeat(30));

    try {
      // 动画时间轴演示
      const timelineNode = this.nodeRegistry.createNode('AnimationTimeline');
      if (timelineNode) {
        const timelineResult = timelineNode.execute({
          duration: 5.0,
          frameRate: 30,
          playbackSpeed: 1.0
        });
        console.log('⏱️ 动画时间轴结果:', timelineResult?.success ? '成功' : '失败');
      }

      // 关键帧编辑演示
      const keyframeNode = this.nodeRegistry.createNode('KeyframeEditor');
      if (keyframeNode) {
        const keyframeResult = keyframeNode.execute({
          time: 2.5,
          value: new Vector3(10, 5, 0),
          interpolation: 'cubic'
        });
        console.log('🔑 关键帧编辑结果:', keyframeResult?.success ? '成功' : '失败');
      }

      // 动画混合演示
      const blendingNode = this.nodeRegistry.createNode('AnimationBlending');
      if (blendingNode) {
        const blendResult = blendingNode.execute({
          animation1Weight: 0.7,
          animation2Weight: 0.3,
          blendMode: 'additive'
        });
        console.log('🔀 动画混合结果:', blendResult?.success ? '成功' : '失败');
      }

      console.log('✅ 动画编辑功能演示完成\n');

    } catch (error) {
      console.error('❌ 动画编辑演示失败:', error);
    }
  }

  /**
   * 演示水体系统功能
   */
  private async demonstrateWaterSystem(): Promise<void> {
    console.log('🌊 水体系统功能演示');
    console.log('-'.repeat(30));

    try {
      // 创建水体演示
      const createWaterNode = this.nodeRegistry.createNode('CreateWaterBody');
      if (createWaterNode) {
        const waterResult = createWaterNode.execute({
          waterType: 'ocean',
          size: new Vector3(100, 10, 100),
          quality: 'clear',
          waveHeight: 2.0
        });
        console.log('💧 创建水体结果:', waterResult?.onCreated ? '成功' : '失败');
      }

      // 水体模拟演示
      const simulationNode = this.nodeRegistry.createNode('WaterSimulation');
      if (simulationNode) {
        const simResult = simulationNode.execute({
          enablePhysics: true,
          waveSpeed: 1.5,
          flowDirection: new Vector3(1, 0, 0)
        });
        console.log('🌀 水体模拟结果:', simResult?.success ? '成功' : '失败');
      }

      console.log('✅ 水体系统功能演示完成\n');

    } catch (error) {
      console.error('❌ 水体系统演示失败:', error);
    }
  }

  /**
   * 演示VR/AR输入功能
   */
  private async demonstrateVRARInput(): Promise<void> {
    console.log('🥽 VR/AR输入功能演示');
    console.log('-'.repeat(30));

    try {
      // VR控制器输入演示
      const vrControllerNode = this.nodeRegistry.createNode('VRControllerInput');
      if (vrControllerNode) {
        const vrResult = vrControllerNode.execute({
          enable: true,
          controllerId: 'right',
          hapticIntensity: 0.5
        });
        console.log('🎮 VR控制器输入结果:', vrResult?.connected ? '已连接' : '未连接');
      }

      // AR手势输入演示
      const arGestureNode = this.nodeRegistry.createNode('ARGestureInput');
      if (arGestureNode) {
        const gestureResult = arGestureNode.execute({
          enable: true,
          gestureTypes: ['tap', 'pinch', 'swipe'],
          minConfidence: 0.8
        });
        console.log('👋 AR手势输入结果:', gestureResult?.gestureType || '无手势');
      }

      // 眼动追踪演示
      const eyeTrackingNode = this.nodeRegistry.createNode('EyeTrackingInput');
      if (eyeTrackingNode) {
        const eyeResult = eyeTrackingNode.execute({
          enable: true,
          calibrated: true
        });
        console.log('👁️ 眼动追踪结果:', eyeResult?.isTracking ? '正在追踪' : '未追踪');
      }

      console.log('✅ VR/AR输入功能演示完成\n');

    } catch (error) {
      console.error('❌ VR/AR输入演示失败:', error);
    }
  }

  /**
   * 演示手势识别功能
   */
  private async demonstrateGestureRecognition(): Promise<void> {
    console.log('✋ 手势识别功能演示');
    console.log('-'.repeat(30));

    try {
      // 手部手势识别演示
      const handGestureNode = this.nodeRegistry.createNode('HandGesture');
      if (handGestureNode) {
        const handResult = handGestureNode.execute({
          enable: true,
          hand: 'right',
          minConfidence: 0.8
        });
        console.log('🤚 手部手势结果:', handResult?.gestureType || '无手势');
      }

      // 身体手势识别演示
      const bodyGestureNode = this.nodeRegistry.createNode('BodyGesture');
      if (bodyGestureNode) {
        const bodyResult = bodyGestureNode.execute({
          enable: true,
          trackingMode: 'full_body'
        });
        console.log('🕺 身体手势结果:', bodyResult?.gestureType || '无手势');
      }

      console.log('✅ 手势识别功能演示完成\n');

    } catch (error) {
      console.error('❌ 手势识别演示失败:', error);
    }
  }

  /**
   * 演示组件系统功能
   */
  private async demonstrateComponentSystem(): Promise<void> {
    console.log('🧩 组件系统功能演示');
    console.log('-'.repeat(30));

    try {
      // 模拟实体
      const mockEntity = { id: 'test-entity', components: new Map() };

      // 添加组件演示
      const addComponentNode = this.nodeRegistry.createNode('AddComponent');
      if (addComponentNode) {
        const addResult = addComponentNode.execute({
          entity: mockEntity,
          componentType: 'Transform',
          componentData: { position: new Vector3(0, 0, 0) }
        });
        console.log('➕ 添加组件结果:', addResult?.success ? '成功' : '失败');
      }

      // 检查组件演示
      const hasComponentNode = this.nodeRegistry.createNode('HasComponent');
      if (hasComponentNode) {
        const hasResult = hasComponentNode.execute({
          entity: mockEntity,
          componentType: 'Transform'
        });
        console.log('🔍 检查组件结果:', hasResult?.hasComponent ? '存在' : '不存在');
      }

      console.log('✅ 组件系统功能演示完成\n');

    } catch (error) {
      console.error('❌ 组件系统演示失败:', error);
    }
  }

  /**
   * 演示变换系统功能
   */
  private async demonstrateTransformSystem(): Promise<void> {
    console.log('🔄 变换系统功能演示');
    console.log('-'.repeat(30));

    try {
      // 模拟实体
      const mockEntity = { 
        id: 'test-entity', 
        transform: { 
          position: new Vector3(5, 10, 15),
          rotation: { x: 0, y: 45, z: 0 }
        }
      };

      // 获取位置演示
      const getPositionNode = this.nodeRegistry.createNode('GetPosition');
      if (getPositionNode) {
        const posResult = getPositionNode.execute({
          entity: mockEntity,
          worldSpace: false
        });
        console.log('📍 获取位置结果:', posResult?.position ? '成功' : '失败');
      }

      // 设置位置演示
      const setPositionNode = this.nodeRegistry.createNode('SetPosition');
      if (setPositionNode) {
        const setPosResult = setPositionNode.execute({
          entity: mockEntity,
          position: new Vector3(10, 20, 30),
          worldSpace: false
        });
        console.log('📌 设置位置结果:', setPosResult?.success ? '成功' : '失败');
      }

      console.log('✅ 变换系统功能演示完成\n');

    } catch (error) {
      console.error('❌ 变换系统演示失败:', error);
    }
  }

  /**
   * 显示统计信息
   */
  private showStatistics(): void {
    console.log('📊 节点注册统计');
    console.log('-'.repeat(30));
    
    const nodeTypes = this.registry.getRegisteredNodeTypes();
    const totalNodes = this.registry.getRegisteredNodeCount();

    console.log(`总注册节点数: ${totalNodes}`);
    console.log(`地形编辑节点: ${nodeTypes.filter(t => t.startsWith('Terrain')).length}`);
    console.log(`动画编辑节点: ${nodeTypes.filter(t => t.startsWith('Animation')).length}`);
    console.log(`水体系统节点: ${nodeTypes.filter(t => t.includes('Water')).length}`);
    console.log(`VR/AR输入节点: ${nodeTypes.filter(t => t.includes('VR') || t.includes('AR') || t.includes('Spatial') || t.includes('EyeTracking') || t.includes('HandTracking') || t.includes('VoiceCommand')).length}`);
    console.log(`手势识别节点: ${nodeTypes.filter(t => t.includes('Gesture')).length}`);
    console.log(`组件系统节点: ${nodeTypes.filter(t => t.includes('Component')).length}`);
    console.log(`变换系统节点: ${nodeTypes.filter(t => t.includes('Position') || t.includes('Rotation')).length}`);
    
    console.log('\n注册的节点类型:');
    nodeTypes.forEach((type, index) => {
      console.log(`${index + 1}. ${type}`);
    });
  }
}

// 运行演示（如果直接执行此文件）
if (require.main === module) {
  const demo = new ContentCreationInputNodesDemo();
  demo.runDemo().catch(console.error);
}
