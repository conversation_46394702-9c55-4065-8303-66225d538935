/**
 * 内容创作与输入节点验证脚本
 * 验证批次10节点注册的正确性
 */

import { NodeRegistry } from './NodeRegistry';
import { ContentCreationInputNodesRegistry } from './ContentCreationInputNodesRegistry';

/**
 * 验证内容创作与输入节点注册
 */
async function verifyContentCreationInputNodes(): Promise<void> {
  console.log('🔍 开始验证内容创作与输入节点注册...');
  console.log('='.repeat(60));

  try {
    // 创建节点注册表实例
    const nodeRegistry = new NodeRegistry();
    const registry = new ContentCreationInputNodesRegistry(nodeRegistry);

    // 注册所有节点
    console.log('📝 注册节点...');
    registry.registerAllNodes();

    // 验证节点数量
    const registeredCount = registry.getRegisteredNodeCount();
    console.log(`✅ 注册节点数量: ${registeredCount}/53`);

    if (registeredCount !== 53) {
      throw new Error(`节点数量不匹配，期望53个，实际${registeredCount}个`);
    }

    // 验证各类别节点
    const nodeTypes = registry.getRegisteredNodeTypes();
    
    // 验证地形编辑节点（12个）
    const terrainNodes = nodeTypes.filter(type => type.startsWith('Terrain'));
    console.log(`🏔️ 地形编辑节点: ${terrainNodes.length}/12`);
    if (terrainNodes.length !== 12) {
      throw new Error(`地形编辑节点数量不匹配，期望12个，实际${terrainNodes.length}个`);
    }

    // 验证动画编辑节点（17个）
    const animationNodes = nodeTypes.filter(type => type.startsWith('Animation'));
    console.log(`🎬 动画编辑节点: ${animationNodes.length}/17`);
    if (animationNodes.length !== 17) {
      throw new Error(`动画编辑节点数量不匹配，期望17个，实际${animationNodes.length}个`);
    }

    // 验证水体系统节点（2个）
    const waterNodes = nodeTypes.filter(type => type.includes('Water'));
    console.log(`🌊 水体系统节点: ${waterNodes.length}/2`);
    if (waterNodes.length !== 2) {
      throw new Error(`水体系统节点数量不匹配，期望2个，实际${waterNodes.length}个`);
    }

    // 验证VR/AR输入节点（8个）
    const vrArNodes = nodeTypes.filter(type => 
      type.includes('VR') || type.includes('AR') || type.includes('Spatial') || 
      type.includes('EyeTracking') || type.includes('HandTracking') || type.includes('VoiceCommand')
    );
    console.log(`🥽 VR/AR输入节点: ${vrArNodes.length}/8`);
    if (vrArNodes.length !== 8) {
      throw new Error(`VR/AR输入节点数量不匹配，期望8个，实际${vrArNodes.length}个`);
    }

    // 验证手势识别节点（4个）
    const gestureNodes = nodeTypes.filter(type => type.includes('Gesture'));
    console.log(`✋ 手势识别节点: ${gestureNodes.length}/4`);
    if (gestureNodes.length !== 4) {
      throw new Error(`手势识别节点数量不匹配，期望4个，实际${gestureNodes.length}个`);
    }

    // 验证组件系统节点（6个）
    const componentNodes = nodeTypes.filter(type => type.includes('Component'));
    console.log(`🧩 组件系统节点: ${componentNodes.length}/6`);
    if (componentNodes.length !== 6) {
      throw new Error(`组件系统节点数量不匹配，期望6个，实际${componentNodes.length}个`);
    }

    // 验证变换系统节点（4个）
    const transformNodes = nodeTypes.filter(type => 
      type.includes('Position') || type.includes('Rotation')
    );
    console.log(`🔄 变换系统节点: ${transformNodes.length}/4`);
    if (transformNodes.length !== 4) {
      throw new Error(`变换系统节点数量不匹配，期望4个，实际${transformNodes.length}个`);
    }

    // 验证节点注册状态
    console.log('\n🔍 验证关键节点注册状态:');
    const keyNodes = [
      'TerrainSculpting', 'AnimationTimeline', 'CreateWaterBody', 
      'VRControllerInput', 'GestureRecognition', 'AddComponent', 'GetPosition'
    ];

    for (const nodeType of keyNodes) {
      const isRegistered = registry.isNodeRegistered(nodeType);
      console.log(`  ${isRegistered ? '✅' : '❌'} ${nodeType}: ${isRegistered ? '已注册' : '未注册'}`);
      if (!isRegistered) {
        throw new Error(`关键节点 ${nodeType} 未注册`);
      }
    }

    // 验证节点分类
    console.log('\n📂 验证节点分类:');
    const allNodes = nodeRegistry.getAllNodes();
    
    const terrainCategory = allNodes.filter(node => node.category === 'Terrain/Editing');
    console.log(`  地形编辑分类: ${terrainCategory.length}个节点`);

    const animationCategory = allNodes.filter(node => node.category === 'Animation/Editing');
    console.log(`  动画编辑分类: ${animationCategory.length}个节点`);

    const waterCategory = allNodes.filter(node => node.category === 'Water/System');
    console.log(`  水体系统分类: ${waterCategory.length}个节点`);

    const vrArCategory = allNodes.filter(node => node.category === 'Input/VRAR');
    console.log(`  VR/AR输入分类: ${vrArCategory.length}个节点`);

    const gestureCategory = allNodes.filter(node => node.category === 'Input/Gesture');
    console.log(`  手势识别分类: ${gestureCategory.length}个节点`);

    const componentCategory = allNodes.filter(node => node.category === 'Entity/Component');
    console.log(`  组件系统分类: ${componentCategory.length}个节点`);

    const transformCategory = allNodes.filter(node => node.category === 'Entity/Transform');
    console.log(`  变换系统分类: ${transformCategory.length}个节点`);

    // 显示所有注册的节点
    console.log('\n📋 所有注册的节点:');
    nodeTypes.sort().forEach((type, index) => {
      console.log(`  ${index + 1}. ${type}`);
    });

    console.log('\n✅ 内容创作与输入节点验证完成！');
    console.log(`总计验证通过: ${registeredCount}个节点`);

  } catch (error) {
    console.error('❌ 验证失败:', error);
    process.exit(1);
  }
}

/**
 * 运行验证
 */
if (require.main === module) {
  verifyContentCreationInputNodes().catch(error => {
    console.error('验证过程中发生错误:', error);
    process.exit(1);
  });
}

export { verifyContentCreationInputNodes };
