/**
 * 内容创作与输入节点注册表
 * 注册批次10：内容创作与输入（53个节点）到编辑器
 * 包括：地形编辑节点(12个) + 动画编辑节点(17个) + 水体系统节点(2个) + VR/AR输入节点(8个) + 手势识别节点(4个) + 组件系统节点(6个) + 变换系统节点(4个)
 */

import { NodeRegistry } from './NodeRegistry';

// 导入地形编辑节点（12个）
import {
  TerrainSculptingNode,
  TerrainPaintingNode
} from '../nodes/terrain/TerrainEditingNodes';

import {
  TerrainTextureNode,
  TerrainVegetationNode
} from '../nodes/terrain/TerrainEditingNodes2';

import {
  TerrainWaterNode,
  TerrainOptimizationNode,
  TerrainExportNode,
  TerrainImportNode
} from '../nodes/terrain/TerrainEditingNodes3';

import {
  TerrainHeightmapNode,
  TerrainErosionNode,
  TerrainNoiseNode,
  TerrainLayerNode
} from '../nodes/terrain/TerrainAdvancedNodes';

// 导入动画编辑节点（17个）
import {
  AnimationTimelineNode,
  KeyframeEditorNode,
  AnimationCurveNode
} from '../nodes/animation/AnimationEditingNodes';

import {
  AnimationLayerNode,
  AnimationBlendingNode,
  AnimationPreviewNode
} from '../nodes/animation/AnimationEditingNodes2';

import {
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode,
  AnimationOptimizationNode
} from '../nodes/animation/AnimationEditingNodes3';

import {
  AnimationSequencerNode,
  AnimationTransitionNode,
  AnimationEventNode,
  AnimationMixerNode,
  AnimationControllerNode,
  AnimationStateNode,
  AnimationClipNode
} from '../nodes/animation/AdvancedAnimationNodes';

// 导入水体系统节点（2个）
import {
  CreateWaterBodyNode,
  WaterSimulationNode
} from '../nodes/water/WaterSystemNodes';

// 导入VR/AR输入节点（8个）
import {
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode,
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../nodes/input/VRARInputNodes';

// 导入手势识别节点（4个）
import {
  GestureRecognitionNode,
  HandGestureNode,
  BodyGestureNode,
  FaceGestureNode
} from '../nodes/mocap/MotionCaptureNodes';

// 导入组件系统节点（6个）
import {
  AddComponentNode,
  RemoveComponentNode,
  GetComponentNode,
  HasComponentNode,
  EnableComponentNode,
  DisableComponentNode
} from '../nodes/entity/ComponentNodes';

// 导入变换系统节点（4个）
import {
  GetPositionNode,
  SetPositionNode,
  GetRotationNode,
  SetRotationNode
} from '../nodes/entity/TransformNodes';

/**
 * 内容创作与输入节点注册表类
 */
export class ContentCreationInputNodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registeredNodes: Set<string> = new Set();

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 注册所有内容创作与输入节点
   */
  public registerAllNodes(): void {
    console.log('开始注册内容创作与输入节点...');

    try {
      // 注册地形编辑节点（12个）
      this.registerTerrainEditingNodes();

      // 注册动画编辑节点（17个）
      this.registerAnimationEditingNodes();

      // 注册水体系统节点（2个）
      this.registerWaterSystemNodes();

      // 注册VR/AR输入节点（8个）
      this.registerVRARInputNodes();

      // 注册手势识别节点（4个）
      this.registerGestureRecognitionNodes();

      // 注册组件系统节点（6个）
      this.registerComponentSystemNodes();

      // 注册变换系统节点（4个）
      this.registerTransformSystemNodes();

      console.log(`内容创作与输入节点注册完成: ${this.registeredNodes.size}个节点`);
      console.log('注册的节点类型:', Array.from(this.registeredNodes).join(', '));

    } catch (error) {
      console.error('内容创作与输入节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册地形编辑节点（12个）
   */
  private registerTerrainEditingNodes(): void {
    console.log('注册地形编辑节点...');

    const terrainNodes = [
      { class: TerrainSculptingNode, type: 'TerrainSculpting', name: '地形雕刻', desc: '地形雕刻工具，支持抬升、降低、平滑等操作' },
      { class: TerrainPaintingNode, type: 'TerrainPainting', name: '地形绘制', desc: '地形纹理绘制工具，支持多层纹理混合' },
      { class: TerrainTextureNode, type: 'TerrainTexture', name: '地形纹理', desc: '地形纹理管理，支持纹理分层和混合' },
      { class: TerrainVegetationNode, type: 'TerrainVegetation', name: '地形植被', desc: '地形植被系统，支持草地、树木等植被生成' },
      { class: TerrainWaterNode, type: 'TerrainWater', name: '地形水体', desc: '地形水体系统，支持河流、湖泊等水体生成' },
      { class: TerrainOptimizationNode, type: 'TerrainOptimization', name: '地形优化', desc: '地形性能优化，支持LOD和剔除优化' },
      { class: TerrainExportNode, type: 'TerrainExport', name: '地形导出', desc: '地形数据导出工具，支持多种格式' },
      { class: TerrainImportNode, type: 'TerrainImport', name: '地形导入', desc: '地形数据导入工具，支持高度图等格式' },
      { class: TerrainHeightmapNode, type: 'TerrainHeightmap', name: '地形高度图', desc: '地形高度图生成和编辑工具' },
      { class: TerrainErosionNode, type: 'TerrainErosion', name: '地形侵蚀', desc: '地形侵蚀效果模拟，支持水蚀和风蚀' },
      { class: TerrainNoiseNode, type: 'TerrainNoise', name: '地形噪声', desc: '地形噪声生成器，支持多种噪声算法' },
      { class: TerrainLayerNode, type: 'TerrainLayer', name: '地形分层', desc: '地形分层管理，支持多层地形编辑' }
    ];

    terrainNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Terrain/Editing',
        desc,
        'terrain',
        '#8BC34A'
      );
      this.registeredNodes.add(type);
    });

    console.log('地形编辑节点注册完成 - 12个节点');
  }

  /**
   * 注册动画编辑节点（17个）
   */
  private registerAnimationEditingNodes(): void {
    console.log('注册动画编辑节点...');

    const animationNodes = [
      { class: AnimationTimelineNode, type: 'AnimationTimeline', name: '动画时间轴', desc: '动画时间轴编辑器，提供时间控制和关键帧管理' },
      { class: KeyframeEditorNode, type: 'KeyframeEditor', name: '关键帧编辑器', desc: '关键帧编辑工具，支持关键帧的添加、删除和修改' },
      { class: AnimationCurveNode, type: 'AnimationCurve', name: '动画曲线', desc: '动画曲线编辑器，支持贝塞尔曲线和缓动函数' },
      { class: AnimationLayerNode, type: 'AnimationLayer', name: '动画层', desc: '动画层管理，支持多层动画混合' },
      { class: AnimationBlendingNode, type: 'AnimationBlending', name: '动画混合', desc: '动画混合工具，支持权重混合和过渡' },
      { class: AnimationPreviewNode, type: 'AnimationPreview', name: '动画预览', desc: '动画预览工具，实时查看动画效果' },
      { class: AnimationExportNode, type: 'AnimationExport', name: '动画导出', desc: '动画数据导出工具，支持多种格式' },
      { class: AnimationImportNode, type: 'AnimationImport', name: '动画导入', desc: '动画数据导入工具，支持FBX、glTF等格式' },
      { class: AnimationValidationNode, type: 'AnimationValidation', name: '动画验证', desc: '动画数据验证工具，检查动画完整性' },
      { class: AnimationOptimizationNode, type: 'AnimationOptimization', name: '动画优化', desc: '动画性能优化，支持关键帧压缩和简化' },
      { class: AnimationSequencerNode, type: 'AnimationSequencer', name: '动画序列器', desc: '动画序列管理，支持复杂动画序列编排' },
      { class: AnimationTransitionNode, type: 'AnimationTransition', name: '动画过渡', desc: '动画过渡控制，支持平滑过渡效果' },
      { class: AnimationEventNode, type: 'AnimationEvent', name: '动画事件', desc: '动画事件系统，支持动画触发事件' },
      { class: AnimationMixerNode, type: 'AnimationMixer', name: '动画混合器', desc: '动画混合器，支持多动画同时播放' },
      { class: AnimationControllerNode, type: 'AnimationController', name: '动画控制器', desc: '动画控制器，提供动画状态机功能' },
      { class: AnimationStateNode, type: 'AnimationState', name: '动画状态', desc: '动画状态管理，支持状态切换和条件' },
      { class: AnimationClipNode, type: 'AnimationClip', name: '动画片段', desc: '动画片段管理，支持片段的创建和编辑' }
    ];

    animationNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Animation/Editing',
        desc,
        'movie',
        '#FF5722'
      );
      this.registeredNodes.add(type);
    });

    console.log('动画编辑节点注册完成 - 17个节点');
  }

  /**
   * 注册水体系统节点（2个）
   */
  private registerWaterSystemNodes(): void {
    console.log('注册水体系统节点...');

    const waterNodes = [
      { class: CreateWaterBodyNode, type: 'CreateWaterBody', name: '创建水体', desc: '创建水体，支持海洋、湖泊、河流等类型' },
      { class: WaterSimulationNode, type: 'WaterSimulation', name: '水体模拟', desc: '水体物理模拟，支持波浪、流动等效果' }
    ];

    waterNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Water/System',
        desc,
        'waves',
        '#2196F3'
      );
      this.registeredNodes.add(type);
    });

    console.log('水体系统节点注册完成 - 2个节点');
  }

  /**
   * 注册VR/AR输入节点（8个）
   */
  private registerVRARInputNodes(): void {
    console.log('注册VR/AR输入节点...');

    const vrArInputNodes = [
      { class: VRControllerInputNode, type: 'VRControllerInput', name: 'VR控制器输入', desc: 'VR控制器输入处理，支持位置、旋转和按钮输入' },
      { class: VRHeadsetTrackingNode, type: 'VRHeadsetTracking', name: 'VR头显追踪', desc: 'VR头显位置和旋转追踪' },
      { class: ARTouchInputNode, type: 'ARTouchInput', name: 'AR触摸输入', desc: 'AR环境中的触摸输入处理' },
      { class: ARGestureInputNode, type: 'ARGestureInput', name: 'AR手势输入', desc: 'AR环境中的手势识别和输入' },
      { class: SpatialInputNode, type: 'SpatialInput', name: '空间输入', desc: '3D空间中的输入处理，支持空间定位' },
      { class: EyeTrackingInputNode, type: 'EyeTrackingInput', name: '眼动追踪输入', desc: '眼动追踪输入处理，支持注视点检测' },
      { class: HandTrackingInputNode, type: 'HandTrackingInput', name: '手部追踪输入', desc: '手部追踪输入处理，支持手势识别' },
      { class: VoiceCommandInputNode, type: 'VoiceCommandInput', name: '语音命令输入', desc: '语音命令识别和处理' }
    ];

    vrArInputNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Input/VRAR',
        desc,
        'view_in_ar',
        '#9C27B0'
      );
      this.registeredNodes.add(type);
    });

    console.log('VR/AR输入节点注册完成 - 8个节点');
  }

  /**
   * 注册手势识别节点（4个）
   */
  private registerGestureRecognitionNodes(): void {
    console.log('注册手势识别节点...');

    const gestureNodes = [
      { class: GestureRecognitionNode, type: 'GestureRecognition', name: '手势识别', desc: '通用手势识别，支持多种手势类型' },
      { class: HandGestureNode, type: 'HandGesture', name: '手部手势', desc: '手部手势识别，支持手指和手掌手势' },
      { class: BodyGestureNode, type: 'BodyGesture', name: '身体手势', desc: '身体手势识别，支持全身动作识别' },
      { class: FaceGestureNode, type: 'FaceGesture', name: '面部手势', desc: '面部表情和手势识别' }
    ];

    gestureNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Input/Gesture',
        desc,
        'gesture',
        '#FF9800'
      );
      this.registeredNodes.add(type);
    });

    console.log('手势识别节点注册完成 - 4个节点');
  }

  /**
   * 注册组件系统节点（6个）
   */
  private registerComponentSystemNodes(): void {
    console.log('注册组件系统节点...');

    const componentNodes = [
      { class: AddComponentNode, type: 'AddComponent', name: '添加组件', desc: '为实体添加组件' },
      { class: RemoveComponentNode, type: 'RemoveComponent', name: '移除组件', desc: '从实体移除组件' },
      { class: GetComponentNode, type: 'GetComponent', name: '获取组件', desc: '获取实体的组件' },
      { class: HasComponentNode, type: 'HasComponent', name: '检查组件', desc: '检查实体是否有指定组件' },
      { class: EnableComponentNode, type: 'EnableComponent', name: '启用组件', desc: '启用实体的组件' },
      { class: DisableComponentNode, type: 'DisableComponent', name: '禁用组件', desc: '禁用实体的组件' }
    ];

    componentNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Entity/Component',
        desc,
        'extension',
        '#607D8B'
      );
      this.registeredNodes.add(type);
    });

    console.log('组件系统节点注册完成 - 6个节点');
  }

  /**
   * 注册变换系统节点（4个）
   */
  private registerTransformSystemNodes(): void {
    console.log('注册变换系统节点...');

    const transformNodes = [
      { class: GetPositionNode, type: 'GetPosition', name: '获取位置', desc: '获取实体的位置信息' },
      { class: SetPositionNode, type: 'SetPosition', name: '设置位置', desc: '设置实体的位置' },
      { class: GetRotationNode, type: 'GetRotation', name: '获取旋转', desc: '获取实体的旋转信息' },
      { class: SetRotationNode, type: 'SetRotation', name: '设置旋转', desc: '设置实体的旋转' }
    ];

    transformNodes.forEach(({ class: NodeClass, type, name, desc }) => {
      this.nodeRegistry.registerNode(
        type,
        NodeClass,
        'Entity/Transform',
        desc,
        'transform',
        '#4CAF50'
      );
      this.registeredNodes.add(type);
    });

    console.log('变换系统节点注册完成 - 4个节点');
  }

  /**
   * 获取注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 获取注册的节点类型列表
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes);
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }
}

// 导出注册函数
export function registerContentCreationInputNodes(nodeRegistry: NodeRegistry): void {
  const registry = new ContentCreationInputNodesRegistry(nodeRegistry);
  registry.registerAllNodes();
}
