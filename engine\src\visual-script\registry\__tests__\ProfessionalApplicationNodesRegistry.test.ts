/**
 * 专业应用领域节点注册表测试
 */

import { ProfessionalApplicationNodesRegistry, PROFESSIONAL_APPLICATION_NODE_TYPES } from '../ProfessionalApplicationNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

describe('ProfessionalApplicationNodesRegistry', () => {
  let registry: ProfessionalApplicationNodesRegistry;
  let nodeRegistry: NodeRegistry;

  beforeEach(() => {
    nodeRegistry = NodeRegistry.getInstance();
    registry = new ProfessionalApplicationNodesRegistry(nodeRegistry);
    registry.reset();
  });

  afterEach(() => {
    registry.reset();
  });

  describe('节点注册', () => {
    test('应该成功注册所有专业应用领域节点', async () => {
      await registry.registerAllNodes();
      
      expect(registry.isRegistered()).toBe(true);
      
      const stats = registry.getRegistrationStats();
      expect(stats.totalNodes).toBe(58); // 总共58个节点
      expect(stats.spatialNodes).toBe(19); // 空间信息节点实际数量
      expect(stats.blockchainNodes).toBe(3);
      expect(stats.learningNodes).toBe(3);
      expect(stats.ragNodes).toBe(4);
      expect(stats.collaborationNodes).toBe(6);
      expect(stats.thirdPartyNodes).toBe(5);
      expect(stats.materialNodes).toBe(10);
      expect(stats.particleNodes).toBe(8);
      expect(stats.errors).toHaveLength(0);
    });

    test('应该防止重复注册', async () => {
      await registry.registerAllNodes();
      const firstStats = registry.getRegistrationStats();
      
      // 尝试再次注册
      await registry.registerAllNodes();
      const secondStats = registry.getRegistrationStats();
      
      expect(firstStats.totalNodes).toBe(secondStats.totalNodes);
      expect(registry.isRegistered()).toBe(true);
    });

    test('应该正确记录注册时间', async () => {
      const startTime = performance.now();
      await registry.registerAllNodes();
      const endTime = performance.now();
      
      const stats = registry.getRegistrationStats();
      expect(stats.registrationTime).toBeGreaterThan(0);
      expect(stats.registrationTime).toBeLessThan(endTime - startTime + 100); // 允许一些误差
    });
  });

  describe('节点类型验证', () => {
    test('应该返回正确的节点类型列表', () => {
      const nodeTypes = registry.getAllRegisteredNodeTypes();
      
      // 验证总数
      expect(nodeTypes).toHaveLength(58);
      
      // 验证包含关键节点类型
      expect(nodeTypes).toContain('CreateGeographicCoordinate');
      expect(nodeTypes).toContain('WalletConnect');
      expect(nodeTypes).toContain('LearningRecord');
      expect(nodeTypes).toContain('RAGQuery');
      expect(nodeTypes).toContain('CollaborationSession');
      expect(nodeTypes).toContain('PaymentSystem');
      expect(nodeTypes).toContain('MaterialEditor');
      expect(nodeTypes).toContain('ParticleSystemEditor');
    });

    test('应该正确分类节点', () => {
      const categories = registry.getNodeCategories();
      
      expect(categories['空间信息']).toBe(19);
      expect(categories['区块链']).toBe(3);
      expect(categories['学习记录']).toBe(3);
      expect(categories['RAG应用']).toBe(4);
      expect(categories['协作功能']).toBe(6);
      expect(categories['第三方集成']).toBe(5);
      expect(categories['材质编辑']).toBe(10);
      expect(categories['粒子编辑']).toBe(8);
    });
  });

  describe('常量验证', () => {
    test('应该包含所有节点类型常量', () => {
      // 验证空间信息节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.CREATE_GEOGRAPHIC_COORDINATE).toBe('CreateGeographicCoordinate');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.COORDINATE_TRANSFORM).toBe('CoordinateTransform');
      
      // 验证区块链节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.WALLET_CONNECT).toBe('WalletConnect');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.SMART_CONTRACT).toBe('SmartContract');
      
      // 验证学习记录节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.LEARNING_RECORD).toBe('LearningRecord');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.LEARNING_PATH).toBe('LearningPath');
      
      // 验证RAG应用节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.RAG_QUERY).toBe('RAGQuery');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.KNOWLEDGE_BASE).toBe('KnowledgeBase');
      
      // 验证协作功能节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.COLLABORATION_SESSION).toBe('CollaborationSession');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.USER_PRESENCE).toBe('UserPresence');
      
      // 验证第三方集成节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.PAYMENT_SYSTEM).toBe('PaymentSystem');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.THIRD_PARTY_API).toBe('ThirdPartyAPI');
      
      // 验证材质编辑节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.MATERIAL_EDITOR).toBe('MaterialEditor');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.MATERIAL_PREVIEW).toBe('MaterialPreview');
      
      // 验证粒子编辑节点常量
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.PARTICLE_SYSTEM_EDITOR).toBe('ParticleSystemEditor');
      expect(PROFESSIONAL_APPLICATION_NODE_TYPES.PARTICLE_EMITTER_EDITOR).toBe('ParticleEmitterEditor');
    });
  });

  describe('错误处理', () => {
    test('应该处理注册过程中的错误', async () => {
      // 模拟注册错误
      const mockNodeRegistry = {
        registerNode: jest.fn().mockImplementation(() => {
          throw new Error('注册失败');
        })
      } as any;
      
      const errorRegistry = new ProfessionalApplicationNodesRegistry(mockNodeRegistry);
      
      await expect(errorRegistry.registerAllNodes()).rejects.toThrow('注册失败');
      
      const stats = errorRegistry.getRegistrationStats();
      expect(stats.errors).toContain('注册失败');
    });
  });

  describe('重置功能', () => {
    test('应该正确重置注册状态', async () => {
      await registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);
      
      registry.reset();
      expect(registry.isRegistered()).toBe(false);
      
      const stats = registry.getRegistrationStats();
      expect(stats.totalNodes).toBe(0);
      expect(stats.spatialNodes).toBe(0);
      expect(stats.blockchainNodes).toBe(0);
      expect(stats.learningNodes).toBe(0);
      expect(stats.ragNodes).toBe(0);
      expect(stats.collaborationNodes).toBe(0);
      expect(stats.thirdPartyNodes).toBe(0);
      expect(stats.materialNodes).toBe(0);
      expect(stats.particleNodes).toBe(0);
      expect(stats.registrationTime).toBe(0);
      expect(stats.errors).toHaveLength(0);
    });
  });

  describe('性能测试', () => {
    test('注册应该在合理时间内完成', async () => {
      const startTime = performance.now();
      await registry.registerAllNodes();
      const endTime = performance.now();
      
      const registrationTime = endTime - startTime;
      expect(registrationTime).toBeLessThan(1000); // 应该在1秒内完成
    });
  });

  describe('集成测试', () => {
    test('应该与NodeRegistry正确集成', async () => {
      const registerNodeSpy = jest.spyOn(nodeRegistry, 'registerNode');
      
      await registry.registerAllNodes();
      
      // 验证调用了正确的次数
      expect(registerNodeSpy).toHaveBeenCalledTimes(58);
      
      // 验证调用参数格式
      expect(registerNodeSpy).toHaveBeenCalledWith(
        expect.any(String), // type
        expect.any(Function), // nodeClass
        expect.any(String), // category
        expect.any(String), // description
        expect.any(String), // icon
        expect.any(String)  // color
      );
    });
  });
});
