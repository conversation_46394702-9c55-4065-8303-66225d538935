/**
 * 内容创作与输入节点注册表测试
 */

import { NodeRegistry } from './NodeRegistry';
import { ContentCreationInputNodesRegistry, registerContentCreationInputNodes } from './ContentCreationInputNodesRegistry';

describe('ContentCreationInputNodesRegistry', () => {
  let nodeRegistry: NodeRegistry;
  let registry: ContentCreationInputNodesRegistry;

  beforeEach(() => {
    nodeRegistry = new NodeRegistry();
    registry = new ContentCreationInputNodesRegistry(nodeRegistry);
  });

  describe('节点注册', () => {
    test('应该成功注册所有内容创作与输入节点', () => {
      expect(() => {
        registry.registerAllNodes();
      }).not.toThrow();

      // 验证注册的节点数量
      expect(registry.getRegisteredNodeCount()).toBe(53);
    });

    test('应该注册正确的节点类型', () => {
      registry.registerAllNodes();
      const registeredTypes = registry.getRegisteredNodeTypes();

      // 验证地形编辑节点（12个）
      const terrainNodes = [
        'TerrainSculpting', 'TerrainPainting', 'TerrainTexture', 'TerrainVegetation',
        'TerrainWater', 'TerrainOptimization', 'TerrainExport', 'TerrainImport',
        'TerrainHeightmap', 'TerrainErosion', 'TerrainNoise', 'TerrainLayer'
      ];
      terrainNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证动画编辑节点（17个）
      const animationNodes = [
        'AnimationTimeline', 'KeyframeEditor', 'AnimationCurve', 'AnimationLayer',
        'AnimationBlending', 'AnimationPreview', 'AnimationExport', 'AnimationImport',
        'AnimationValidation', 'AnimationOptimization', 'AnimationSequencer',
        'AnimationTransition', 'AnimationEvent', 'AnimationMixer',
        'AnimationController', 'AnimationState', 'AnimationClip'
      ];
      animationNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证水体系统节点（2个）
      const waterNodes = ['CreateWaterBody', 'WaterSimulation'];
      waterNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证VR/AR输入节点（8个）
      const vrArInputNodes = [
        'VRControllerInput', 'VRHeadsetTracking', 'ARTouchInput', 'ARGestureInput',
        'SpatialInput', 'EyeTrackingInput', 'HandTrackingInput', 'VoiceCommandInput'
      ];
      vrArInputNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证手势识别节点（4个）
      const gestureNodes = ['GestureRecognition', 'HandGesture', 'BodyGesture', 'FaceGesture'];
      gestureNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证组件系统节点（6个）
      const componentNodes = [
        'AddComponent', 'RemoveComponent', 'GetComponent',
        'HasComponent', 'EnableComponent', 'DisableComponent'
      ];
      componentNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });

      // 验证变换系统节点（4个）
      const transformNodes = ['GetPosition', 'SetPosition', 'GetRotation', 'SetRotation'];
      transformNodes.forEach(nodeType => {
        expect(registeredTypes).toContain(nodeType);
        expect(registry.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该正确分类节点', () => {
      registry.registerAllNodes();

      // 验证节点是否在NodeRegistry中正确注册
      const allNodes = nodeRegistry.getAllNodes();
      
      // 检查地形编辑节点分类
      const terrainNodes = allNodes.filter(node => node.category === 'Terrain/Editing');
      expect(terrainNodes.length).toBe(12);

      // 检查动画编辑节点分类
      const animationNodes = allNodes.filter(node => node.category === 'Animation/Editing');
      expect(animationNodes.length).toBe(17);

      // 检查水体系统节点分类
      const waterNodes = allNodes.filter(node => node.category === 'Water/System');
      expect(waterNodes.length).toBe(2);

      // 检查VR/AR输入节点分类
      const vrArInputNodes = allNodes.filter(node => node.category === 'Input/VRAR');
      expect(vrArInputNodes.length).toBe(8);

      // 检查手势识别节点分类
      const gestureNodes = allNodes.filter(node => node.category === 'Input/Gesture');
      expect(gestureNodes.length).toBe(4);

      // 检查组件系统节点分类
      const componentNodes = allNodes.filter(node => node.category === 'Entity/Component');
      expect(componentNodes.length).toBe(6);

      // 检查变换系统节点分类
      const transformNodes = allNodes.filter(node => node.category === 'Entity/Transform');
      expect(transformNodes.length).toBe(4);
    });
  });

  describe('注册函数', () => {
    test('registerContentCreationInputNodes应该正确注册所有节点', () => {
      expect(() => {
        registerContentCreationInputNodes(nodeRegistry);
      }).not.toThrow();

      const allNodes = nodeRegistry.getAllNodes();
      expect(allNodes.length).toBeGreaterThanOrEqual(53);
    });
  });

  describe('错误处理', () => {
    test('应该处理重复注册', () => {
      registry.registerAllNodes();
      const firstCount = registry.getRegisteredNodeCount();

      // 再次注册不应该增加节点数量
      registry.registerAllNodes();
      const secondCount = registry.getRegisteredNodeCount();

      expect(secondCount).toBe(firstCount);
    });

    test('应该处理无效的节点类型查询', () => {
      registry.registerAllNodes();
      expect(registry.isNodeRegistered('NonExistentNode')).toBe(false);
    });
  });

  describe('节点统计', () => {
    test('应该返回正确的节点统计信息', () => {
      registry.registerAllNodes();

      expect(registry.getRegisteredNodeCount()).toBe(53);
      expect(registry.getRegisteredNodeTypes().length).toBe(53);
      
      // 验证节点类型数组不包含重复项
      const nodeTypes = registry.getRegisteredNodeTypes();
      const uniqueTypes = [...new Set(nodeTypes)];
      expect(nodeTypes.length).toBe(uniqueTypes.length);
    });
  });

  describe('节点分布验证', () => {
    test('应该验证各类别节点数量正确', () => {
      registry.registerAllNodes();
      const nodeTypes = registry.getRegisteredNodeTypes();

      // 按前缀统计节点数量
      const terrainCount = nodeTypes.filter(type => type.startsWith('Terrain')).length;
      const animationCount = nodeTypes.filter(type => type.startsWith('Animation')).length;
      const waterCount = nodeTypes.filter(type => type.includes('Water')).length;
      const vrArCount = nodeTypes.filter(type => 
        type.includes('VR') || type.includes('AR') || type.includes('Spatial') || 
        type.includes('EyeTracking') || type.includes('HandTracking') || type.includes('VoiceCommand')
      ).length;
      const gestureCount = nodeTypes.filter(type => type.includes('Gesture')).length;
      const componentCount = nodeTypes.filter(type => type.includes('Component')).length;
      const transformCount = nodeTypes.filter(type => 
        type.includes('Position') || type.includes('Rotation')
      ).length;

      expect(terrainCount).toBe(12);
      expect(animationCount).toBe(17);
      expect(waterCount).toBe(2);
      expect(vrArCount).toBe(8);
      expect(gestureCount).toBe(4);
      expect(componentCount).toBe(6);
      expect(transformCount).toBe(4);

      // 总计应该等于53
      const totalCount = terrainCount + animationCount + waterCount + vrArCount + 
                        gestureCount + componentCount + transformCount;
      expect(totalCount).toBe(53);
    });
  });
});
